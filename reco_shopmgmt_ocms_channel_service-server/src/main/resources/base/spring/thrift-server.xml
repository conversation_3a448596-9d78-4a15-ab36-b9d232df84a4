<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.sankuai.meituan.shangou.empower.ocms"/>

    <!-- 服务鉴权 -->
    <bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
        <property name="appkey" value="${app.name}"/>
    </bean>
    <!-- channel 连接粒度鉴权 -->
    <bean id="defaultAuthHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler">
        <property name="authDataSource" ref="kmsAuthDataSource"/>
        <property name="authType" value="channelAuth"/>
    </bean>
    <!-- request 接口粒度鉴权 -->
    <!--<bean id="requestAuthHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler">-->
        <!--<property name="authDataSource" ref="kmsAuthDataSource"/>-->
        <!--<property name="authType" value="requestAuth"/>-->
    <!--</bean>-->

    <!-- 评价线程池配置 -->
    <bean id="commentServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommentThriftService"/>
        <constructor-arg name="threadPoolName" value="comment-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 评价方法级线程池配置，用于低频接口，如reply/queryCommentRule。避免和主要接口性能相互影响 -->
    <bean id="commentMethodServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommentThriftServiceMethod"/>
        <constructor-arg name="threadPoolName" value="order-comment-method-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 订单线程池配置 -->
    <bean id="orderServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService"/>
        <constructor-arg name="threadPoolName" value="order-docking-thread-pool"/>
        <constructor-arg name="min" value="20"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 订单线程池配置 -->
    <bean id="orderExtraServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.MtExtraInfoThriftService"/>
        <constructor-arg name="threadPoolName" value="order-common-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- qnh订单上行动态线程池 -->
    <!-- 禁止使用自定义线程池，需使用Rhino动态线程池-->
    <bean name="qnhUpstreamOrderServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService"/>
        <constructor-arg name="threadPoolName" value="qnh-order-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 订单下行动态线程池 -->
    <bean name="orderNotifyServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderCallbackThriftService"/>
        <constructor-arg name="threadPoolName" value="order-notify-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 配送上行线程池配置 -->
    <bean name="deliveryDockingServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelDeliveryDockingThriftService"/>
        <constructor-arg name="threadPoolName" value="delivery-docking-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 微信公众号线程池 -->
    <!-- 禁止使用自定义线程池，需使用Rhino动态线程池-->
    <bean id="wechatServiceExecutor" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="10"/>
        <constructor-arg index="1" value="50"/>
        <constructor-arg index="2" value="30"/>
        <constructor-arg index="3" value="SECONDS"/>
        <constructor-arg index="4">
            <bean class="java.util.concurrent.LinkedBlockingQueue">
                <constructor-arg value="150"/>
            </bean>
        </constructor-arg>
        <constructor-arg index="5">
            <bean class="com.meituan.service.mobile.mtthrift.server.MTDefaultThreadFactory">
                <constructor-arg value="order-thrift-thread-pool"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- 订单线程池配置 -->
    <!-- 禁止使用自定义线程池，需使用Rhino动态线程池-->
    <bean id="settlementServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="settlementServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="settlement-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 门店线程池配置 -->
    <!-- 禁止使用自定义线程池，需使用Rhino动态线程池-->
    <bean id="poiServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="poiServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="poi-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 渠道应用信息线程池配置 -->
    <!-- 禁止使用自定义线程池，需使用Rhino动态线程池-->
    <bean id="channelAppServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="channelAppServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="channel-app-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 分类线程池配置 -->
    <bean id="categoryServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="categoryServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="category-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 价格线程池配置 -->
    <bean id="priceServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="priceServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="price-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 库存线程池配置 -->
    <bean name="stockUpstreamServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="stockUpstreamService"/>
        <constructor-arg name="threadPoolName" value="stock-thrift-thread-pool"/>
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 库存线程池配置 -->
    <bean name="stockDownstreamServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="stockDownstreamService"/>
        <constructor-arg name="threadPoolName" value="stock-downstream-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 发票线程池配置 -->
    <bean name="invoiceServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="invoiceServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="invoice-up-thrift-thread-pool"/>
        <constructor-arg name="min" value="30"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>
    <bean name="channelInvoiceCallbackThriftServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="channelInvoiceCallbackThriftServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="invoice-down-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>


    <!-- 商品线程池配置 -->
    <bean id="skuServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="skuServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="sku-thrift-thread-pool"/>
        <constructor-arg name="min" value="20"/>
        <constructor-arg name="max" value="70"/>
        <constructor-arg name="workQueueSize" value="-1"/>
    </bean>

    <bean id="spuServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="spuServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="spu-thrift-thread-pool"/>
        <constructor-arg name="min" value="20"/>
        <constructor-arg name="max" value="70"/>
        <constructor-arg name="workQueueSize" value="-1"/>
    </bean>

    <bean id="spuCleanerServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="spuCleanerServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="spu-cleaner-thrift-thread-pool"/>
        <constructor-arg name="min" value="50"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="200"/>
    </bean>

    <!-- 商品回调线程池 -->
    <bean id="channelCallbackServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="channelCallbackServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="channel-callback-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 质量分线程池配置 -->
    <bean id="qualityScoreServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="qualityScoreServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="quality-score-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 促销线程池配置 -->
    <bean id="activityServiceExecutor" class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="activityServiceExecutor"/>
        <constructor-arg name="threadPoolName" value="activity-service-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 促销线程池配置 -->
    <bean id="activityDictServiceExecutor" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="10"/>
        <constructor-arg index="1" value="50"/>
        <constructor-arg index="2" value="30"/>
        <constructor-arg index="3" value="SECONDS"/>
        <constructor-arg index="4">
            <bean class="java.util.concurrent.LinkedBlockingQueue">
                <constructor-arg value="150"/>
            </bean>
        </constructor-arg>
        <constructor-arg index="5">
            <bean class="com.meituan.service.mobile.mtthrift.server.MTDefaultThreadFactory">
                <constructor-arg value="activity-dict-thrift-thread-pool"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- 会员线程池配置 -->
    <bean id="memberServiceExecutor" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="10"/>
        <constructor-arg index="1" value="50"/>
        <constructor-arg index="2" value="30"/>
        <constructor-arg index="3" value="SECONDS"/>
        <constructor-arg index="4">
            <bean class="java.util.concurrent.LinkedBlockingQueue">
                <constructor-arg value="150"/>
            </bean>
        </constructor-arg>
        <constructor-arg index="5">
            <bean class="com.meituan.service.mobile.mtthrift.server.MTDefaultThreadFactory">
                <constructor-arg value="member-thrift-thread-pool"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- elm权限令牌获取线程池配置 -->
    <bean id="accessTokenServiceExecutor" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="10"/>
        <constructor-arg index="1" value="50"/>
        <constructor-arg index="2" value="30"/>
        <constructor-arg index="3" value="SECONDS"/>
        <constructor-arg index="4">
            <bean class="java.util.concurrent.LinkedBlockingQueue">
                <constructor-arg value="150"/>
            </bean>
        </constructor-arg>
        <constructor-arg index="5">
            <bean class="com.meituan.service.mobile.mtthrift.server.MTDefaultThreadFactory">
                <constructor-arg value="poi-thrift-thread-pool"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- 鉴权线程池配置 -->
    <bean id="authServiceExecutor" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="10"/>
        <constructor-arg index="1" value="50"/>
        <constructor-arg index="2" value="30"/>
        <constructor-arg index="3" value="SECONDS"/>
        <constructor-arg index="4">
            <bean class="java.util.concurrent.LinkedBlockingQueue">
                <constructor-arg value="150"/>
            </bean>
        </constructor-arg>
        <constructor-arg index="5">
            <bean class="com.meituan.service.mobile.mtthrift.server.MTDefaultThreadFactory">
                <constructor-arg value="auth-thrift-thread-pool"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- 鉴权线程池配置 -->
    <bean id="appServiceExecutor" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg index="0" value="10"/>
        <constructor-arg index="1" value="50"/>
        <constructor-arg index="2" value="30"/>
        <constructor-arg index="3" value="SECONDS"/>
        <constructor-arg index="4">
            <bean class="java.util.concurrent.LinkedBlockingQueue">
                <constructor-arg value="150"/>
            </bean>
        </constructor-arg>
        <constructor-arg index="5">
            <bean class="com.meituan.service.mobile.mtthrift.server.MTDefaultThreadFactory">
                <constructor-arg value="app-thrift-thread-pool"/>
            </bean>
        </constructor-arg>
    </bean>

    <!-- 配送线程池配置 -->
    <bean id="deliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="deliveryServiceRhinoExecutor"/>
        <constructor-arg name="threadPoolName" value="delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>


    <bean id="channelAggDeliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryThriftService"/>
        <constructor-arg name="threadPoolName" value="channel-agg-delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <bean id="dapChannelAggDeliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService"/>
        <constructor-arg name="threadPoolName" value="dap-channel-agg-delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <bean id="maltChannelAggDeliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService"/>
        <constructor-arg name="threadPoolName" value="malt-channel-agg-delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <bean id="farmDeliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiThriftService"/>
        <constructor-arg name="threadPoolName" value="farm-delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <bean id="dapDeliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapPaoTuiThriftService"/>
        <constructor-arg name="threadPoolName" value="dap-delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <bean id="platformDeliveryServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="platformDeliveryServiceRhinoExecutor"/>
        <constructor-arg name="threadPoolName" value="platform-delivery-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>


    <!-- 内部调用线程池配置 -->
    <bean id="innerThriftServiceExecutor"
          class="com.meituan.service.mobile.mtthrift.util.ThreadPoolCreator"
          factory-method="getWorkerExecutor">
        <constructor-arg name="rhinoThreadPoolKey" value="com.sankuai.meituan.shangou.empower.ocms.channel.delivery.thrift.inner.FarmPaoTuiInnerThriftService"/>
        <constructor-arg name="threadPoolName" value="inner-thrift-thread-pool"/>
        <constructor-arg name="min" value="10"/>
        <constructor-arg name="max" value="50"/>
        <constructor-arg name="workQueueSize" value="150"/>
    </bean>

    <!-- 渠道订单对接服务 -->
    <bean id="channelOrderThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelOrderThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="orderServiceExecutor"/>
    </bean>

    <!-- 牵牛花订单对接服务 -->
    <bean id="qnhOrderThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.QnhOrderThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="qnhUpstreamOrderServiceExecutor"/>
    </bean>

    <!-- 跑腿配送对内服务 -->
    <bean id="paotuiDeliveryInnerThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.delivery.thrift.inner.FarmPaoTuiInnerThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="innerThriftServiceExecutor"/>
    </bean>


    <!-- 渠道订单对接服务 -->
    <bean id="weChatCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.WeChatCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="wechatServiceExecutor"/>
    </bean>


    <!-- 渠道结算对接服务 -->
    <bean id="channelSettlementThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelSettlementThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="settlementServiceExecutor"/>
    </bean>
    <bean id="qnhSettlementThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.QnhSettlementThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="settlementServiceExecutor"/>
    </bean>
    <!-- 渠道门店对接服务 -->
    <bean id="channelPoiThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelPoiThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="poiServiceExecutor"/>
    </bean>

    <!-- 渠道订单回调下行服务 -->
    <bean id="channelOrderCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelOrderCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="orderNotifyServiceExecutor"/>
    </bean>
    <!-- 商品回调下行服务 -->
    <bean id="channelCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="channelCallbackServiceExecutor"/>
    </bean>
    <!-- 渠道门店回调下行服务 -->
    <bean id="channelPoiCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelPoiCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="poiServiceExecutor"/>
    </bean>

    <!-- 渠道商品前台分类对接服务 -->
    <bean id="channelCategoryThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelCategoryThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="categoryServiceExecutor"/>
    </bean>

    <!-- 渠道价格对接服务 -->
    <bean id="channelPriceThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelPriceThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="priceServiceExecutor"/>
    </bean>

    <!-- 渠道商品对接服务 -->
    <bean id="channelSkuThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelSkuThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="skuServiceExecutor"/>
    </bean>
    <!-- 渠道商品对接服务SPU -->
    <bean id="channelSpuThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelSpuThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <bean id="channelSpuCleanerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelSpuCleanerThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="spuCleanerServiceExecutor"/>
    </bean>

    <!-- 渠道总部商品对接服务SPU -->
    <bean id="channelMerchantSpuThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelMerchantSpuThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <bean id="channelOfflineCategoryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelOfflineCategoryThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>
    <!-- 渠道发票对接服务 -->
    <bean id="qnhInvoiceThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.invoice.QnhInvoiceThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="invoiceServiceExecutor"/>
    </bean>

    <bean id="channelInvoiceCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelInvoiceCallbackThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="channelInvoiceCallbackThriftServiceExecutor"/>
    </bean>

    <!-- 渠道总部素材对接服务SPU -->
    <bean id="channelMaterialThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelMaterialThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <!-- 渠道虚拟配置对接服务-->
    <bean id="channelVirtualConfigThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelVirtualConfigThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <!-- 渠道总部商品品牌对接服务SPU -->
    <bean id="channelBrandThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelBrandThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <!-- 渠道商品品牌类目服务 -->
    <bean id="channelCategoryBrandThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelCategoryBrandThriftServiceImpl"/>
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <bean id="channelVideoThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelVideoThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>

    <bean id="channelQualityThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelQualityThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="qualityScoreServiceExecutor"/>
    </bean>

    <!-- 渠道库存对接服务 -->
    <bean id="channelStockThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelStockThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="stockUpstreamServiceExecutor"/>
    </bean>

    <!-- 美团闪购提单库存校验服务 -->
    <bean id="sgTSCCStockCheckThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.SgTSCCStockCheckThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="stockDownstreamServiceExecutor"/>
    </bean>

    <!-- 牵牛花库存对接服务 -->
    <bean id="qnhStockThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.QnhStockThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="stockUpstreamServiceExecutor"/>
    </bean>

    <!-- 牵牛花库存对接服务 -->
    <bean id="qnhProductThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.QnhProductThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="stockUpstreamServiceExecutor"/>
    </bean>

    <!-- 渠道基本信息对接服务 -->
    <bean id="channelBaseMsgThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelBaseMsgThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="poiServiceExecutor"/>
    </bean>

    <!-- 渠道促销对接服务 -->
    <bean id="channelActivityThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelActivityThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>

    <!-- 活动类型信息查询服务 -->
    <bean id="channelActivityDictThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelActivityDictThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="activityDictServiceExecutor"/>
    </bean>

    <!-- 渠道应用信息查询服务 -->
    <bean id="channelAppThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelAppThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="channelAppServiceExecutor"/>
    </bean>

    <!-- 渠道会员对接服务 -->
    <bean id="channelMemberCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelMemberCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="memberServiceExecutor"/>
    </bean>

    <!-- 饿了么令牌获取下行服务 -->
    <bean id="channelAccessTokenCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelAccessTokenCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="accessTokenServiceExecutor"/>
    </bean>

    <!-- 渠道评价对接服务 -->
    <bean id="channelCommentThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelCommentThriftServiceImpl"/>
        <property name="serviceExecutor" ref="commentServiceExecutor"/>
        <property name="methodExecutor">
            <map>
                <entry key="reply" value-ref="commentMethodServiceExecutor"/>
                <entry key="queryCommentRule" value-ref="commentMethodServiceExecutor"/>
            </map>
        </property>
    </bean>

    <!-- 公共服务 -->
    <bean id="channelCommonThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelCommonThriftServiceImpl"/>
        <property name="serviceExecutor" ref="authServiceExecutor"/>
    </bean>

    <!-- 公共应用信息 -->
    <bean id="commonAppThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="commonAppThriftServiceImpl"/>
        <property name="serviceExecutor" ref="appServiceExecutor"/>
    </bean>


    <!-- 渠道配送服务 -->
    <bean id="channelDeliveryThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelDeliveryThriftServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>

    <!-- 渠道配送回调服务 -->
    <bean id="channelDeliveryCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelDeliveryCallbackThriftServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>

    <!-- 渠道配送回调服务 -->
    <bean id="channelPoiShippingThriftV2ServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelPoiShippingThriftV2ServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>

    <!-- 门店配送服务 -->
    <bean id="channelPoiShippingThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelPoiShippingThriftServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>

    <!-- 门店配送服务 -->
    <bean id="channelDeliverySyncThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelDeliverySyncThriftServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>

    <!-- 三方聚合运力配送服务 -->
    <bean id="channelAggDeliveryThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelAggDeliveryThriftServiceImpl"/>
        <property name="serviceExecutor" ref="channelAggDeliveryServiceExecutor"/>
    </bean>

    <bean id="dapChannelAggDeliveryThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="dapChannelAggDeliveryThriftServiceImpl"/>
        <property name="serviceExecutor" ref="dapChannelAggDeliveryServiceExecutor"/>
    </bean>

    <bean id="maltChannelAggDeliveryThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="maltChannelAggDeliveryThriftServiceImpl"/>
        <property name="serviceExecutor" ref="maltChannelAggDeliveryServiceExecutor"/>
    </bean>

    <!-- 平台配送服务 -->
    <bean id="orderChannelDeliveryThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="orderChannelDeliveryThriftServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>

    <bean id="farmPaoTuiThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="farmPaoTuiThriftServiceImpl"/>
        <property name="serviceExecutor" ref="farmDeliveryServiceExecutor"/>
    </bean>



    <bean id="channelAggDeliveryCallbackThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelAggDeliveryCallbackThriftServiceImpl"/>
        <property name="serviceExecutor" ref="channelAggDeliveryServiceExecutor"/>
    </bean>

    <bean id="farmDeliveryCallbackThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="farmDeliveryCallbackThriftServiceImpl"/>
        <property name="serviceExecutor" ref="farmDeliveryServiceExecutor"/>
    </bean>


    <bean id="dapDeliveryCallbackThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="dapDeliveryCallbackThriftServiceImpl"/>
        <property name="serviceExecutor" ref="dapDeliveryServiceExecutor"/>
    </bean>

    <bean id="dapPaoTuiThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="dapPaoTuiThriftServiceImpl"/>
        <property name="serviceExecutor" ref="dapDeliveryServiceExecutor"/>
    </bean>

    <bean id="dapDeliveryQueryThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="dapDeliveryQueryThriftServiceImpl"/>
        <property name="serviceExecutor" ref="dapDeliveryServiceExecutor"/>
    </bean>
    <bean id="channelWeChatThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelWeChatThriftServiceImpl"/>
        <property name="serviceExecutor" ref="accessTokenServiceExecutor"/>
    </bean>

    <bean id="yzUserThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="yzUserThriftServiceImpl"/>
        <property name="serviceExecutor" ref="accessTokenServiceExecutor"/>
    </bean>
    <bean id="orderCompensationThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="orderCompensationThriftServiceImpl"/>
        <property name="serviceExecutor" ref="qnhUpstreamOrderServiceExecutor"/>
    </bean>

    <bean id="farmPaoTuiPostThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.delivery.thrift.upstream.FarmPaoTuiPostThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="farmDeliveryServiceExecutor"/>
    </bean>

    <!--医药对接阿里健康-->
    <bean id="aliHealthChannelThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.medicine.AliHealthChannelThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="skuServiceExecutor"/>
    </bean>

    <!--  医药渠道网关回调服务  -->
    <bean id="medicineChannelCallbackThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.MedicineChannelCallbackThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="channelCallbackServiceExecutor"/>
    </bean>
    <bean id="mtDeliveryCallbackThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="mtDeliveryCallbackThriftServiceImpl"/>
        <property name="serviceExecutor" ref="platformDeliveryServiceExecutor"/>
    </bean>
    <bean id="mtOrderNotifyThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="mtOrderNotifyThriftServiceImpl"/>
        <property name="serviceExecutor" ref="deliveryServiceExecutor"/>
    </bean>
    <bean id="elmDeliveryOperationCallbackThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="elmDeliveryOperationCallbackThriftServiceImpl"/>
        <property name="serviceExecutor" ref="platformDeliveryServiceExecutor"/>
    </bean>
    <bean id="channelSpuThriftServiceV2Bean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelSpuThriftServiceV2Impl"/>
        <property name="serviceExecutor" ref="spuServiceExecutor"/>
    </bean>
    <bean id="orderPlatformDeliveryOperateThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="orderPlatformDeliveryOperateThriftServiceImpl"/>
        <property name="serviceExecutor" ref="platformDeliveryServiceExecutor"/>
    </bean>

    <bean id="mtPromotionThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="mtPromotionThriftServiceImpl"/>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>

    <bean id="eblsPromotionThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="eblsPromotionThriftServiceImpl"/>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>

    <bean id="jddjPromotionThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="jddjPromotionThriftServiceImpl"/>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>

    <bean id="dyPromotionThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="dyPromotionThriftServiceImpl"/>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>

    <bean id="channelProductUpdateRuleThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelProductUpdateRuleThriftServiceImpl"/>
        <property name="serviceExecutor" ref="channelCallbackServiceExecutor"/>
    </bean>

    <bean id="mtAppThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="mtAppThriftServiceImpl"/>
        <property name="serviceExecutor" ref="channelAppServiceExecutor"/>
    </bean>

    <bean id = "mtExtraInfoThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="mtExtraInfoThriftServiceImpl"/>
        <property name="serviceExecutor" ref="orderExtraServiceExecutor"/>
    </bean>
    <bean id="txdPromotionThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="txdPromotionThriftServiceImpl"/>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>


    <bean id="channelActivityPromotionThriftServiceBean"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="channelActivityPromotionThriftServiceImpl"/>
        <property name="serviceExecutor" ref="activityServiceExecutor"/>
    </bean>

    <bean id="EBLSMessageServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl" ref="EBLSMessageService"/>
    </bean>

    <!-- 渠道上行配送对接服务 -->
    <bean id="channelDeliveryDockingThriftServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
        <property name="serviceImpl">
            <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.ChannelDeliveryDockingThriftServiceImpl" />
        </property>
        <property name="serviceExecutor" ref="deliveryDockingServiceExecutor"/>
    </bean>

    <!-- 上行服务 -->
    <bean id="multiUpServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
      init-method="publish" destroy-method="destroy">
        <property name="port" value="8090"/>
        <property name="appKey" value="${app.name}"/>
        <property name="serviceProcessorMap">
            <map>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.IEBLSMessageService"
                       value-ref="EBLSMessageServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService"
                  value-ref="channelOrderThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService"
                       value-ref="qnhOrderThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCategoryThriftService"
                       value-ref="channelCategoryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelVirtualConfigThriftService"
                       value-ref="channelVirtualConfigThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPriceThriftService"
                       value-ref="channelPriceThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSkuThriftService"
                       value-ref="channelSkuThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSpuThriftService"
                       value-ref="channelSpuThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelMerchantSpuThriftService"
                       value-ref="channelMerchantSpuThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelOfflineCategoryThriftService"
                       value-ref="channelOfflineCategoryThriftService"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelBrandThriftService"
                       value-ref="channelBrandThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelCategoryBrandThriftService"
                       value-ref="channelCategoryBrandThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.thrift.ChannelMaterialThriftService"
                       value-ref="channelMaterialThriftService"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.qnh.service.invoice.QnhInvoiceThriftService"
                       value-ref="qnhInvoiceThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelStockThriftService"
                       value-ref="channelStockThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhStockThriftService"
                       value-ref="qnhStockThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.qnh.service.QnhProductThriftService"
                       value-ref="qnhProductThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelBaseMsgThriftService"
                       value-ref="channelBaseMsgThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityThriftService"
                       value-ref="channelActivityThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommentThriftService"
                       value-ref="channelCommentThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCommonThriftService"
                       value-ref="channelCommonThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryThriftService"
                       value-ref="channelDeliveryThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiThriftService"
                       value-ref="channelPoiThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelAppThriftService"
                       value-ref="channelAppThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiShippingThriftService"
                       value-ref="channelPoiShippingThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliverySyncThriftService"
                       value-ref="channelDeliverySyncThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSettlementThriftService"
                       value-ref="channelSettlementThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhSettlementThriftService"
                       value-ref="qnhSettlementThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryThriftService"
                       value-ref="channelAggDeliveryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService"
                       value-ref="dapChannelAggDeliveryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService"
                       value-ref="maltChannelAggDeliveryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderChannelDeliveryThriftService"
                       value-ref="orderChannelDeliveryThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiThriftService"
                       value-ref="farmPaoTuiThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelWeChatThriftService"
                       value-ref="channelWeChatThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.YzUserThriftService"
                       value-ref="yzUserThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderCompensationThriftService"
                       value-ref="orderCompensationThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelVideoThriftService"
                       value-ref="channelVideoThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelQualityThriftService"
                       value-ref="channelQualityThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapPaoTuiThriftService"
                       value-ref="dapPaoTuiThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapDeliveryQueryThriftService"
                       value-ref="dapDeliveryQueryThriftServiceBean" />
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelSpuThriftServiceV2"
                       value-ref="channelSpuThriftServiceV2Bean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.OrderPlatformDeliveryOperateThriftService"
                       value-ref="orderPlatformDeliveryOperateThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelSpuCleanerThriftService"
                       value-ref="channelSpuCleanerThriftService"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtPromotionThriftService"
                       value-ref="mtPromotionThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.EblsPromotionThriftService"
                       value-ref="eblsPromotionThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.JddjPromotionThriftService"
                       value-ref="jddjPromotionThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DyPromotionThriftService"
                       value-ref="dyPromotionThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtAppThriftService"
                       value-ref="mtAppThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.medicine.thrift.AliHealthChannelThriftService"
                       value-ref="aliHealthChannelThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelProductUpdateRuleThriftService"
                       value-ref="channelProductUpdateRuleThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelActivityPromotionThriftService"
                       value-ref="channelActivityPromotionThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.TxdPromotionThriftService"
                       value-ref="txdPromotionThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtExtraInfoThriftService"
                       value-ref="mtExtraInfoThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelDeliveryDockingThriftService"
                       value-ref="channelDeliveryDockingThriftServiceBean"/>
            </map>
        </property>
        <property name="filters">
            <list>
                <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.filter.AccessLogFilter"/>
            </list>
        </property>
        <property name="authHandler" ref="defaultAuthHandler"/>
    </bean>

    <bean id="innerThriftServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="port" value="8091"/>
        <property name="appKey" value="${app.name}"/>
        <property name="serviceProcessorMap">
            <map>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiInnerThriftService"
                       value-ref="paotuiDeliveryInnerThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelActivityDictThriftService"
                  value-ref="channelActivityDictThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.CommonAppThriftService"
                       value-ref="commonAppThriftServiceBean"/>
            </map>
        </property>
        <property name="filters">
            <list>
                <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.filter.AccessLogFilter"/>
            </list>
        </property>
        <property name="authHandler" ref="defaultAuthHandler"/>
    </bean>

    <!-- 下行服务 -->
    <bean id="multiDownServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="port" value="${appPort}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="serviceProcessorMap">
            <map>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderCallbackThriftService"
                       value-ref="channelOrderCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelCallbackThriftService"
                       value-ref="channelCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelMemberCallbackThriftService"
                       value-ref="channelMemberCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelDeliveryCallbackThriftService"
                       value-ref="channelDeliveryCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelPoiShippingThriftV2Service"
                       value-ref="channelPoiShippingThriftV2ServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelPoiCallbackThriftService"
                       value-ref="channelPoiCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelAccessTokenCallbackThriftService"
                       value-ref="channelAccessTokenCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelAggDeliveryCallbackThriftService"
                       value-ref="channelAggDeliveryCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmDeliveryCallbackThriftService"
                       value-ref="farmDeliveryCallbackThriftServiceBean"/>
                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelInvoiceCallbackThriftService"
                       value-ref="channelInvoiceCallbackThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.WeChatCallbackThriftService"
                       value-ref="weChatCallbackThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.FarmPaoTuiPostThriftService"
                       value-ref="farmPaoTuiPostThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MedicineChannelCallbackThriftService"
                       value-ref="medicineChannelCallbackThriftServiceBean" />

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapDeliveryCallbackThriftService"
                       value-ref="dapDeliveryCallbackThriftServiceBean" />

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtDeliveryCallbackThriftService"
                       value-ref="mtDeliveryCallbackThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ElmDeliveryOperationCallbackThriftService"
                       value-ref="elmDeliveryOperationCallbackThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MtOrderNotifyThriftService"
                       value-ref="mtOrderNotifyThriftServiceBean"/>

                <entry key="com.sankuai.meituan.shangou.platform.open.mms.thrift.service.SgTSCCStockCheckThriftService"
                       value-ref="sgTSCCStockCheckThriftServiceBean"/>
            </map>
        </property>
        <property name="filters">
            <list>
                <bean class="com.sankuai.meituan.shangou.empower.ocms.channel.filter.AccessLogFilter"/>
            </list>
        </property>
        <property name="authHandler" ref="defaultAuthHandler"/>
    </bean>



</beans>