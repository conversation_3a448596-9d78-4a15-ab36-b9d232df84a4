
#服务端口
appPort: 8081

#允许bean覆盖
spring:
  main:
    allow-bean-definition-overriding: true

# zebra参数配置
zebra:
  enabled: false
  jdbcRef: recofreego_reco_ocms_test # jdbcRef配置, 通过profile隔离
  poolType: druid
  minPoolSize: 5
  maxPoolSize: 30
  initialPoolSize: 5
  checkoutTimeout: 1000
  maxIdleTime: 1800
  idleConnectionTestPeriod: 60
  acquireRetryAttempts: 3
  acquireRetryDelay: 300
  maxStatements: 0
  maxStatementsPerConnection: 100
  numHelperThreads: 6
  maxAdministrativeTaskTime: 5
  preferredTestQuery: SELECT 1

squirrel:
  clusterName: redis-reco-freegoadmin_dev
  mtTokenClusterName: redis-reco-freegoadmin_dev
  commonClusterName: redis-sg-common_dev
  yzTokenClusterName: redis-sg-common_dev
  douyinTokenClusterName: redis-sg-common_dev
  drunkHorseClusterName: redis-sg-drunkhorse_qa
  redisSgNewSupplyClusterName: redis-sg-newsupply-ofc_qa
  readTimeout: 100
  routerType: master-slave
  poolMaxIdle: 16
  poolMaxTotal: 32
  poolWaitMillis: 500
  poolMinIdle: 1


qnh:
  url:
    base: https://openapi-stg.m-glory.net

# raptor上报URL
raptor:
  productMonitorUrl: http://catfront.51ping.com/api/metric?v=1&sdk=1.9.5&webVersion=Alpha&p=com.sankuai.sgshopmgmt.product.monitor

#  @liuyonggao
drunkhorse:
  url:
    base: http://waimaiopen-in.vip.sankuai.com/api/v1
    detail: /buyorder/getOrderDetail
    cityDegrade: /ecommerce/order/getPrivacyDegradeCityIdPrefixes
    orderConfirm: /buyorder/confirm
    orderCancel: /buyorder/cancel
    orderLogisticsSync: /buyorder/logistics/sync
    orderLogisticsSuspend: /buyorder/logistics/suspend
    orderGetPartRefundFoods: /buyorder/getPartRefundFoods
    orderAfsApplyListUrl: /buyorder/getOrderRefundDetail
    getOrderDaySeq: /buyorder/getOrderDaySeq
    getOrderIdByDaySeq: /buyorder/getOrderIdByDaySeq
    uploadArrivalPictures: /buyorder/logistics/uploadArrivalPictures
    deliveryMethodChange: /buyorder/tripartite/delivery

mt:
  url:
    base: http://waimaiopen-in.vip.sankuai.com/api/v1
    gwBase: https://waimaiopen.meituan.com/api/v1   #医药审核接口请求域名
    createAppBase: https://opendj.meituan.com
    stockQuery: http://waimaiopen-in.vip.sankuai.com/api/v1
    shoplist: /poi/getids
    shopget: /poi/mget
    skuDetail: /retail/get
    skulist: /retail/list
    frontCatList: /retailCat/list
    skuDelete: /retail/delete
    categoryCreate: /retailCat/update
    getSpTagIds: /retail/getSpTagIds
    categoryAttrList: /gw/category/attr/list
    categoryRequired: /retail/field/required/info
    categoryAttrValueList: /gw/category/attr/value/list
    productRules: /retail/product/rules
    orderDetail: /order/getOrderDetail
    preparationMealComplete: /order/preparationMealComplete
    orderStatus: /order/viewstatus
    orderConfirm: /order/confirm
    orderCancel: /order/cancel
    actRetailDiscountList: /act/retail/discount/list
    logisticsStatus: /order/logistics/status
    reviewAfterSales: /ecommerce/order/reviewAfterSales
    settlementBaseV1: https://waimaiopen.meituan.com/api/v1
    billList: /bill/list
    commentQuery: /comment/query
    commentReply: /poi/comment/add_reply
    orderAfsApplyList: /ecommerce/order/getOrderRefundDetail
    orderDelivering: /order/delivering
    orderArrived: /order/arrived
    orderLogisticsSync: /ecommerce/order/logistics/sync
    deliveringRiderPointSync: /ecommerce/order/delivering/riderPosition/batch
    deliveryCompletedRiderPointSync: /ecommerce/order/deliveryCompleted/riderPosition/batch
    orderLogisticsChangePoiSelf: /order/logistics/change/poi_self
    orderGetPartRefundFoods: /order/getPartRefundFoods
    spuDetail: /retail/get
    spuList: /retail/list
    shippingDelete: /shipping/delete
    shippingReset: /shipping/resetSelfDeliveryArea
    shippingSave: /shipping/save
    shippingSpecSave: /shipping/spec/save
    shippingBatchSave: /shipping/batchsave
    corporateShippingBatchSave: shipping/corporate/batchsave
    shippingList: /shipping/list
    corporateShippingList: shipping/corporate/list
    shippingBatchList: /ecommerce/shipping/batchList
    actAllGetByAppFoodCodes: /act/all/get/byAppFoodCodes
    batchFetchAbnormalOrder: /order/batchFetchAbnormalOrder
    spuAuditStatus: /retail/audit/status
    spuMedicineAuditStatus: /gw/medicine/getProductAuditStatus
    spuQualityProblems: /retail/quality/info
    submitAppealInfo: /retail/appeal
    actCanModifyPrice: /act/can/modify/price
    weightRefundCalculate: /ecommerce/order/getUnitPartRefundPreview
    recommendChannelCategory: /retail/recommend/tag
    batchGetAppSpuCodesBySkuIds: /gw/retail/batchGetAppSpuCodesBySkuIds
    headquarterSpuList: /retail/hq/list
    spuNormAuditDelete: /retail/normAudit/deleted
    batchGetSpuId: /retail/batchGetSpuId
    batchUploadImageUrl: /ecommerce/image/batchUploadImageUrl
    getOriginList: /gw/retail/origin/list
    getUnitPartRefundFoods: /order/getPartRefundFoods
    getUnitPartRefundFoodsV2: /order/getUnitPartRefundFoods
    getOrderIdByDaySeq: /ecommerce/order/getOrderIdByDaySeq
    getAfterSaleOrders: /ecommerce/order/getAfterSaleOrders
    getOrderDaySeq: /order/getOrderDaySeq
    batchUpdateAppSpuCodeByOriginUrl: /retail/batchUpdateAppSpuCodeByOrigin
    getOrderIdByPage: /ecommerce/order/getOrderIdByPage

    getAmountPartRefundFoods: /ecommerce/order/getPartRefundFoods
    poiSettleCategoryList: /ecommerce/poi/settle/category/list
    getReturnDuringDelivery: /ecommerce/order/returnGoods/status
    returnGoodsAudit: /ecommerce/order/returnGoods/check
    healthBatchDecrypt: /ecommerce/health/batchDecrypt
    getCompensateInfo: /order/batchCompensationOrder
    getOrderExtraInfo: https://waimaiopen.meituan.com/api/v1/order/extraInfo
    poiKeyPointUpload: /ecommerce/poi/keyPointUpload
    getOrderOtherRefundDetail: /ecommerce/order/getOrderOtherRefundDetail
    getOrderPrivacyPhone: /ecommerce/order/getOrderPrivacyPhone
    getOrderRefundGoodsFee: /ecommerce/order/returnGoods/fee/detail

mt_drunkhorse:
  url:
    base: http://waimaiopen-in.vip.sankuai.com/api/v1

elm:
  url:
    base: https://api-be.ele.me
    mediaUpload: https://media-retail.ele.me/api/invoke
    shoplist: shop.list
    shopIdList: shop.id.list
    shopBatchGet: shop.batch.get
    shopget: shop.get
    skushopauthget: sku.shop.auth.get
    skushopauthcontrol: sku.shop.auth.control
    skulist: sku.list
    frontCatList: sku.shop.category.get
    catlist: sku.category.list
    categoryAttrList: sku.category.property.list
    skuDelete: sku.delete
    categoryCreate: sku.shop.category.create
    categoryUpdate: sku.shop.category.update
    orderDetail: order.get
    orderStatus: order.status.get
    deliveryStatus: order.delivery.get
    orderSendout: order.selfDeliveryStateSync
    orderComplete: order.complete
    orderSwitchSelfDelivery: order.switchselfdelivery
    getDeliveryStateRecord: order.getDeliveryStateRecord
    getAccessToken: https://open-auth.ele.me/oauth/token
    oauth2Token: https://open-auth.ele.me/oauth2/token
    selfDeliveryLocationSync: order.selfDeliveryLocationSync

jddj:
  url:
    base: https://openapi.jddj.com
    frontCatList: /djapi/pms/queryCategoriesByOrgCode
    skulist: /djapi/pms/querySkuInfos
    skuUpdate: /djapi/pms/sku/updateSku
    shoplist: /djapi/store/getStationsByVenderId
    shopget: /djapi/storeapi/getStoreInfoByStationNo
    catlist: /djapi/api/queryChildCategoriesForOP
    categoryAttrList: /djapi/pms/getSkuCategoryAttrByCategoryId
    brandlist: /djapi/pms/queryPageBrandInfo
    categoryCreate: /djapi/pms/addShopCategory
    categoryUpdate: /djapi/pms/updateShopCategory
    orderDetail: /djapi/order/es/query
    verificationUpdateToken: /djapi/ApplicationService/verificationUpdateToken
    categorySaleAttr: /djapi/OpenPmsCategorySaleAttrService/queryPmsCategorySaleAttr
    recommendBrandAndCate: /djapi/pms/getSkuCateBrandBySkuName

douyin:
  url:
    base: https://openapi-fxg.jinritemai.com

mtdelivery:
  url:
    base: http://openapi.banma.vip.sankuai.com/api
bird:
  url:
    base: https://exam-anubis.ele.me/anubis-webapi
    callback: http://waimai-openapi.apigw.test.meituan.com/api/channel/bird-delivery/order-status
xm:
  pubId: 137540504358
  key: 0cW9122418557010

maltfarm:
  url:
    base: http://receiver.m.maiyatian.com
    callback: https://open.maiyatian.com/callback/delivery/?channel=shuguopai

dap:
  url:
    base: http://39821-ohlns-sl-dap.banma.test.sankuai.com
    callback: http://39821-ohlns-sl-dap.banma.test.sankuai.com/api/channel/common/linkInfos

weixin:
  url:
    base: https://api.weixin.qq.com/cgi-bin

---

spring:
  profiles: dev
# zebra参数配置
zebra:
  jdbcRef: recofreego_reco_ocms_dev  #jdbcRef配置, 通过profile隔离
squirrel:
  clusterName: redis-reco-freegoadmin_dev
  mtTokenClusterName: redis-reco-freegoadmin_dev
  commonClusterName: redis-sg-common_dev
  yzTokenClusterName: redis-sg-common_dev
  douyinTokenClusterName: redis-sg-common_dev
  drunkHorseClusterName: redis-sg-drunkhorse_qa
  redisSgNewSupplyClusterName: redis-sg-newsupply-ofc_qa
bird:
  url:
    base: https://exam-anubis.ele.me/anubis-webapi
    callback: http://waimai-openapi.apigw.dev.meituan.com/api/channel/bird-delivery/order-status

qnh:
  url:
    base: http://waimai-openapi.apigw.test.meituan.com/api

blueInvoiceUpload: https://openapi.shangou.test.sankuai.com/api/v1/ecommerce/invoice/upload
redInvoiceUpload: https://openapi.shangou.test.sankuai.com/api/v1/ecommerce/invoice/cancellationResultUpload

mt_drunkhorse:
  url:
    base: http://openapi.shangou.test.sankuai.com/api/v1

# raptor上报URL
raptor:
  productMonitorUrl: http://catfront.51ping.com/api/metric?v=1&sdk=1.9.5&webVersion=Alpha&p=com.sankuai.sgshopmgmt.product.monitor

---

spring:
  profiles: test

mt:
  url:
    base: http://waimaiopen-in.vip.sankuai.com/api/v1

zebra:
  jdbcRef: recofreego_reco_ocms_test_db_test #替换为自己test环境的jdbcRef
squirrel:
  clusterName: redis-reco-freegoadmin_qa
  mtTokenClusterName: redis-reco-freegoadmin_qa
  commonClusterName: redis-sg-common_qa
  yzTokenClusterName: redis-sg-common_qa
  douyinTokenClusterName: redis-sg-common_qa
  drunkHorseClusterName: redis-sg-drunkhorse_qa
  redisSgNewSupplyClusterName: redis-sg-newsupply-ofc_qa

bird:
  url:
    base: https://exam-anubis.ele.me/anubis-webapi
    callback: http://waimai-openapi.apigw.test.meituan.com/api/channel/bird-delivery/order-status

#mt:
#  url:
#    base: http://open.waimai.st.sankuai.com/api/v1

drunkhorse:
  url:
    base: http://selftest-250427-101150-987-sl-openapi.shangou.test.sankuai.com/api/v1

xm:
  pubId: 137439106257
  key: 1208915t0740j612

maltfarm:
  url:
    base: http://test-receiver.m.maiyatian.com
    callback: http://open.test.maiyatian.com/callback/delivery/?channel=shuguopai

dap:
  url:
    base: http://dap.banma.test.sankuai.com
    callback: http://dap.banma.test.sankuai.com/api/channel/paotui/statusNotify

qnh:
  url:
    base: http://waimai-openapi.apigw.test.meituan.com/api

blueInvoiceUploadUrl: https://openapi.shangou.test.sankuai.com/api/v1/ecommerce/invoice/upload
redInvoiceUploadUrl: https://openapi.shangou.test.sankuai.com/api/v1/ecommerce/invoice/cancellationResultUpload

mt_drunkhorse:
  url:
    base: http://selftest-250427-101150-987-sl-openapi.shangou.test.sankuai.com/api/v1

# raptor上报URL
raptor:
  productMonitorUrl: http://catfront.51ping.com/api/metric?v=1&sdk=1.9.5&webVersion=Alpha&p=com.sankuai.sgshopmgmt.product.monitor

---

spring:
  profiles: beta
zebra:
  jdbcRef: recofreego_reco_ocms_beta #替换为自己beta环境的jdbcRef
bird:
  url:
    base: https://exam-anubis.ele.me/anubis-webapi
    callback: http://waimai-openapi.apigw.test.meituan.com/api/channel/bird-delivery/order-status

maltfarm:
  url:
    base: https://test-receiver.m.maiyatian.com
    callback: http://open.test.maiyatian.com/callback/delivery/?channel=shuguopai

dap:
  url:
    base: http://dap.banma.test.sankuai.com
    callback: http://dap.banma.test.sankuai.com/api/channel/paotui/statusNotify

blueInvoiceUploadUrl: https://openapi.shangou.test.sankuai.com/api/v1/ecommerce/invoice/upload
redInvoiceUploadUrl: https://openapi.shangou.test.sankuai.com/api/v1/ecommerce/invoice/cancellationResultUpload

# raptor上报URL
raptor:
  productMonitorUrl: http://catfront.51ping.com/api/metric?v=1&sdk=1.9.5&webVersion=Alpha&p=com.sankuai.sgshopmgmt.product.monitor

---

spring:
  profiles: staging

mt:
  url:
    base: http://waimaiopen-in.vip.sankuai.com/api/v1

zebra:
  jdbcRef: reco_ocms_product #替换为自己staging环境的jdbcRef
squirrel:
  clusterName: redis-reco-freegoadmin_stage
  mtTokenClusterName: redis-reco-freegoadmin_product
  commonClusterName: redis-sg-common_stage
  yzTokenClusterName: redis-sg-common_product
  douyinTokenClusterName: redis-sg-common_product
  drunkHorseClusterName: redis-sg-drunkhorse_stage
  redisSgNewSupplyClusterName: redis-sg-newsupply-ofc_stage

bird:
  url:
    base: https://exam-anubis.ele.me/anubis-webapi
    callback: https://waimai-openapi.st.meituan.com/api/channel/bird-delivery/order-status
xm:
  pubId: 137540504358
  key: 0cW9122418557010

maltfarm:
  url:
    base: http://test-receiver.m.maiyatian.com
    callback: http://open.test.maiyatian.com/callback/delivery/?channel=shuguopai

dap:
  url:
    base: http://dap.banma.st.sankuai.com
    callback: http://dap.banma.st.sankuai.com/api/channel/paotui/statusNotify

drunkhorse:
  url:
    base: http://open.waimai.st.sankuai.com/api/v1

qnh:
  url:
    base: https://openapi-stg.m-glory.net

mt_drunkhorse:
  url:
    base: https://openapi.shangou.st.sankuai.com/api/v1

blueInvoiceUploadUrl: https://waimaiopen.meituan.com/api/v1/ecommerce/invoice/upload
redInvoiceUploadUrl: https://waimaiopen.meituan.com/api/v1/ecommerce/invoice/cancellationResultUpload
# raptor上报URL
raptor:
  productMonitorUrl: http://catfront.dianping.com/api/metric?v=1&sdk=1.9.5&webVersion=Alpha&p=com.sankuai.sgshopmgmt.product.monitor

---

spring:
  profiles: prod
zebra:
  jdbcRef: reco_ocms_product #替换为自己prod环境的jdbcRef
squirrel:
  clusterName: redis-reco-freegoadmin_product
  mtTokenClusterName: redis-reco-freegoadmin_product
  commonClusterName: redis-sg-common_product
  yzTokenClusterName: redis-sg-common_product
  douyinTokenClusterName: redis-sg-common_product
  drunkHorseClusterName: redis-sg-drunkhorse_product
  redisSgNewSupplyClusterName: redis-sg-newsupply-ofc_product

bird:
  url:
    base: https://open-anubis.ele.me/anubis-webapi
    callback: https://waimai-openapi.meituan.com/api/channel/bird-delivery/order-status
xm:
  pubId: 137540504358
  key: 0cW9122418557010

maltfarm:
  url:
    base: http://receiver.m.maiyatian.com
    callback: https://open.maiyatian.com/callback/delivery/?channel=shuguopai

dap:
  url:
    base: https://dap.meituan.com
    callback: https://dap.meituan.com/api/channel/paotui/statusNotify

drunkhorse:
  url:
    base: https://waimaiopen-in.vip.sankuai.com/api/v1

qnh:
  url:
    base: http://openapi.m-glory.net

blueInvoiceUploadUrl: https://waimaiopen.meituan.com/api/v1/ecommerce/invoice/upload
redInvoiceUploadUrl: https://waimaiopen.meituan.com/api/v1/ecommerce/invoice/cancellationResultUpload

mt_drunkhorse:
  url:
    base: http://waimaiopen-in.vip.sankuai.com/api/v1

# raptor上报URL
raptor:
  productMonitorUrl: http://catfront.dianping.com/api/metric?v=1&sdk=1.9.5&webVersion=Alpha&p=com.sankuai.sgshopmgmt.product.monitor
