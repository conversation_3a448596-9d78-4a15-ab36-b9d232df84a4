package com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-09-23
 * @email <EMAIL>
 */
@Data
@ThriftStruct
public class QueryLogisticsCodesCodeRequest {

    //租户id
    @ThriftField(1)
    private Long tenantId;

    //渠道门店code
    @ThriftField(2)
    private List<String> channelStoreCodes;

    //渠道门店code
    @ThriftField(3)
    private int channelId;

}
