package com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping;

import com.facebook.swift.codec.ThriftField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
//重量加价对象
public class WeightRaiseFee {

    //开始重量 单位：千克
    @ThriftField(1)
    private String startWeight;

    //结束重量 单位：千克
    @ThriftField(2)
    private String endWeight;

    //加价步长 每多少（raiseStep）千克加价
    @ThriftField(3)
    private String raiseStep;

    //加价金额 单位：元
    @ThriftField(4)
    private String raiseFee;
}