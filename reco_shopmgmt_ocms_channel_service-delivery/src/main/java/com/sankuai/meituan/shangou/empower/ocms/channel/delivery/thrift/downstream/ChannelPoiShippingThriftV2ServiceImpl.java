package com.sankuai.meituan.shangou.empower.ocms.channel.delivery.thrift.downstream;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.Constant;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.TimeMarkupFactor;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelPoiInfo;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.DistanceMarkupFactor;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.MarkupExecuteTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.WeightMarkupFactor;
import com.sankuai.meituan.shangou.empower.ocms.channel.merchantspu.dto.ChannelStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.QueryLogisticsCodeResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.dto.shipping.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.ChannelPoiShippingThriftV2Service;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.route.RouteServiceFactory;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/9/9
 */

@Slf4j
@Service
public class ChannelPoiShippingThriftV2ServiceImpl implements ChannelPoiShippingThriftV2Service {

    @Resource
    private RouteServiceFactory routeServiceFactory;

    @Override
    public QueryLogisticsCodeResponse queryChannelDeliveryLogicsCode(QueryLogisticsCodesCodeRequest request) {

        List<ChannelPoiInfo> channelPoiInfoList = routeServiceFactory.selectChannelPoiService(request.getChannelId(), request.getTenantId()).queryChannelPoiInfoList(
                request.getTenantId(), request.getChannelStoreCodes(), request.getChannelId()
        );
        Map<String, List<String>> resMap = Optional.ofNullable(channelPoiInfoList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(
                        ChannelPoiInfo::getApp_poi_code,
                        it -> StringUtils.isNotBlank(it.getLogistics_codes()) ? Lists.newArrayList(it.getLogistics_codes().split(Constant.COMMA)) : Lists.newArrayList(),
                        (older, newer) -> newer
                ));
        return new QueryLogisticsCodeResponse(ChannelStatus.buildSuccess(), resMap);
    }

    @Override
    public ResultStatus updateDeliveryFeeRuleSetting(DeliveryFeeRuleSettingRequest request) {
        ResultStatus resultStatus = createSuccessResultStatus();

        // 美团比较特殊，配送范围相关接口有两套：自配送和企客
        if (isMeituanChannel(request.getChannelId())) {
            return handleMeituanDeliveryFeeUpdate(request, resultStatus);
        }

        return resultStatus;
    }

    /**
     * 创建成功状态的ResultStatus
     */
    private ResultStatus createSuccessResultStatus() {
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ResultCode.SUCCESS.getCode());
        resultStatus.setMsg(StringUtils.EMPTY);
        return resultStatus;
    }

    /**
     * 判断是否为美团渠道
     */
    private boolean isMeituanChannel(Integer channelId) {
        return Objects.equals(channelId, DynamicChannelType.MEITUAN.getChannelId());
    }

    /**
     * 处理美团配送费更新
     */
    private ResultStatus handleMeituanDeliveryFeeUpdate(DeliveryFeeRuleSettingRequest request, ResultStatus resultStatus) {
        boolean containsCorporateDelivery = containsCorporateDelivery(request.getLogisticsCodes());

        if (containsCorporateDelivery) {
            // 企客相关接口 - 暂未实现
            return handleMtCorporateDeliveryUpdate(request, resultStatus);
        } else {
            // 非企客配送，走自配接口
            return handleMtSelfDeliveryUpdate(request, resultStatus);
        }
    }

    /**
     * 判断是否包含企客配送
     */
    private boolean containsCorporateDelivery(List<String> logisticsCodes) {
        return logisticsCodes.stream()
                .anyMatch(code -> MccConfigUtil.getCorporateLogisticsCodes().contains(code));
    }

    /**
     * 处理自配送更新
     */
    private ResultStatus handleMtSelfDeliveryUpdate(DeliveryFeeRuleSettingRequest request, ResultStatus resultStatus) {
        // 查询现有配送信息
        QueryPoiShippingResponse queryResponse = queryExistingShippingInfo(request);
        if (!isQuerySuccessful(queryResponse)) {
            return createErrorResultStatus(queryResponse);
        }

        List<PoiShippingInfoDTO> existingShippingInfoList = queryResponse.getPoiShippingInfoDTOList();
        if (CollectionUtils.isEmpty(existingShippingInfoList)) {
            log.info("No existing shipping info found for request: {}", request);
            return resultStatus;
        }

        // 批量更新配送信息
        BatchUpdatePoiShippingRequest batchUpdateRequest = createBatchUpdateRequest(request, existingShippingInfoList);
        routeServiceFactory.selectChannelPoiShippingService(request.getChannelId(), request.getTenantId())
                .batchUpdatePoiShipping(batchUpdateRequest);

        return resultStatus;
    }

    /**
     * 处理自配送更新
     */
    private ResultStatus handleMtCorporateDeliveryUpdate(DeliveryFeeRuleSettingRequest request, ResultStatus resultStatus) {
        // 查询现有配送信息
        QueryPoiShippingResponse queryResponse = queryExistingShippingInfoForCorporate(request);
        if (!isQuerySuccessful(queryResponse)) {
            return createErrorResultStatus(queryResponse);
        }

        List<PoiShippingInfoDTO> existingShippingInfoList = queryResponse.getPoiShippingInfoDTOList();
        if (CollectionUtils.isEmpty(existingShippingInfoList)) {
            log.info("No existing shipping info found for request: {}", request);
            return resultStatus;
        }

        // 批量更新配送信息
        BatchUpdatePoiShippingRequest batchUpdateRequest = createBatchUpdateRequest(request, existingShippingInfoList);
        routeServiceFactory.selectChannelPoiShippingService(request.getChannelId(), request.getTenantId())
                .batchUpdateCorporatePoiShipping(batchUpdateRequest);

        return resultStatus;
    }

    /**
     * 查询现有配送信息
     */
    private QueryPoiShippingResponse queryExistingShippingInfo(DeliveryFeeRuleSettingRequest request) {
        QueryPoiShippingRequest queryRequest = new QueryPoiShippingRequest();
        queryRequest.setTenantId(request.getTenantId());
        queryRequest.setPoiId(request.getStoreId());
        queryRequest.setChannelId(request.getChannelId());

        return routeServiceFactory.selectChannelPoiShippingService(request.getChannelId(), request.getTenantId())
                .queryPoiShipping(queryRequest);
    }

    /**
     * 查询现有配送信息
     */
    private QueryPoiShippingResponse queryExistingShippingInfoForCorporate(DeliveryFeeRuleSettingRequest request) {
        QueryPoiShippingRequest queryRequest = new QueryPoiShippingRequest();
        queryRequest.setTenantId(request.getTenantId());
        queryRequest.setPoiId(request.getStoreId());
        queryRequest.setChannelId(request.getChannelId());

        return routeServiceFactory.selectChannelPoiShippingService(request.getChannelId(), request.getTenantId())
                .queryCorporatePoiShipping(queryRequest);
    }

    /**
     * 判断查询是否成功
     */
    private boolean isQuerySuccessful(QueryPoiShippingResponse response) {
        return Objects.equals(response.getCode(), ResultCode.SUCCESS.getCode());
    }

    /**
     * 创建错误状态的ResultStatus
     */
    private ResultStatus createErrorResultStatus(QueryPoiShippingResponse queryResponse) {
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(queryResponse.getCode());
        resultStatus.setMsg(queryResponse.getMsg());
        return resultStatus;
    }

    /**
     * 创建批量更新请求
     */
    private BatchUpdatePoiShippingRequest createBatchUpdateRequest(DeliveryFeeRuleSettingRequest request,
                                                                   List<PoiShippingInfoDTO> existingShippingInfoList) {
        BatchUpdatePoiShippingRequest batchUpdateRequest = new BatchUpdatePoiShippingRequest();
        batchUpdateRequest.setTenantId(request.getTenantId());
        batchUpdateRequest.setShopId(request.getStoreId());
        batchUpdateRequest.setChannelId(request.getChannelId());

        List<PoiShippingInfo> updatedShippingInfoList = existingShippingInfoList.stream()
                .map(dto -> buildUpdatedPoiShippingInfo(dto, request))
                .collect(Collectors.toList());

        batchUpdateRequest.setPoiShippingInfo(updatedShippingInfoList);
        return batchUpdateRequest;
    }

    /**
     * 构建更新后的配送信息
     */
    private PoiShippingInfo buildUpdatedPoiShippingInfo(PoiShippingInfoDTO dto, DeliveryFeeRuleSettingRequest request) {
        PoiShippingInfo poiShippingInfo = new PoiShippingInfo();

        // 设置基础信息
        setBasicShippingInfo(poiShippingInfo, dto, request);

        // 设置各种加价规则
        setDistanceMarkupRules(poiShippingInfo, request);
        setWeightMarkupRules(poiShippingInfo, request);
        setTimeMarkupRules(poiShippingInfo, request);

        return poiShippingInfo;
    }

    /**
     * 设置基础配送信息
     */
    private void setBasicShippingInfo(PoiShippingInfo poiShippingInfo, PoiShippingInfoDTO dto,
                                      DeliveryFeeRuleSettingRequest request) {
        poiShippingInfo.setAppShippingCode(Integer.parseInt(dto.getAppShippingCode()));
        poiShippingInfo.setCoordinateList(convertToCoordinateList(dto.getArea()));
        poiShippingInfo.setMtShippingId(dto.getMtShippingId());
        poiShippingInfo.setMinPriceDouble(new BigDecimal(request.getBaseMinimumDeliveryFee()).doubleValue());
        poiShippingInfo.setShippingFee(new BigDecimal(request.getBaseDeliveryFee()).doubleValue());
    }

    /**
     * 转换区域坐标列表
     */
    private List<Coordinate> convertToCoordinateList(List<Coordination> areaList) {
        return Optional.ofNullable(areaList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(this::convertAreaToCoordinate)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个区域坐标
     */
    private Coordinate convertAreaToCoordinate(Coordination area) {
        Coordinate coordinate = new Coordinate();
        coordinate.setLongitude(area.getY());
        coordinate.setLatitude(area.getX());
        return coordinate;
    }

    /**
     * 设置距离加价规则
     */
    private void setDistanceMarkupRules(PoiShippingInfo poiShippingInfo, DeliveryFeeRuleSettingRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDistanceRaiseFeeList())) {
            poiShippingInfo.setDistanceMarkupExecuteType(MarkupExecuteTypeEnum.ENABLE.getCode());

            List<DistanceMarkupFactor> distanceMarkupFactors = request.getDistanceRaiseFeeList().stream()
                    .map(this::convertToDistanceMarkupFactor)
                    .collect(Collectors.toList());

            poiShippingInfo.setDistanceMarkupFactors(JSON.toJSONString(distanceMarkupFactors));
        } else {
            poiShippingInfo.setDistanceMarkupExecuteType(MarkupExecuteTypeEnum.DISABLE.getCode());
        }
    }

    /**
     * 转换距离加价因子
     */
    private DistanceMarkupFactor convertToDistanceMarkupFactor(DistanceRaiseFee distanceRaiseFee) {
        DistanceMarkupFactor factor = new DistanceMarkupFactor();
        factor.setDistance(new BigDecimal(distanceRaiseFee.getStartDistance()).doubleValue());
        factor.setMarkupNum(new BigDecimal(distanceRaiseFee.getRaiseFee()).doubleValue());
        return factor;
    }

    /**
     * 设置重量加价规则
     */
    private void setWeightMarkupRules(PoiShippingInfo poiShippingInfo, DeliveryFeeRuleSettingRequest request) {
        if (CollectionUtils.isNotEmpty(request.getWeightRaiseFeeList())) {
            poiShippingInfo.setWeightMarkupExecuteType(MarkupExecuteTypeEnum.ENABLE.getCode());

            List<WeightMarkupFactor> weightMarkupFactors = request.getWeightRaiseFeeList().stream()
                    .map(this::convertToWeightMarkupFactor)
                    .collect(Collectors.toList());

            poiShippingInfo.setWeightMarkupFactors(JSON.toJSONString(weightMarkupFactors));
        } else {
            poiShippingInfo.setWeightMarkupExecuteType(MarkupExecuteTypeEnum.DISABLE.getCode());
        }
    }

    /**
     * 转换重量加价因子
     */
    private WeightMarkupFactor convertToWeightMarkupFactor(WeightRaiseFee weightRaiseFee) {
        WeightMarkupFactor factor = new WeightMarkupFactor();
        factor.setWeight(new BigDecimal(weightRaiseFee.getStartWeight()).doubleValue());
        factor.setStep(new BigDecimal(weightRaiseFee.getRaiseStep()).doubleValue());
        factor.setMarkupNum(new BigDecimal(weightRaiseFee.getRaiseFee()).doubleValue());
        return factor;
    }

    /**
     * 设置时间加价规则
     */
    private void setTimeMarkupRules(PoiShippingInfo poiShippingInfo, DeliveryFeeRuleSettingRequest request) {
        if (CollectionUtils.isNotEmpty(request.getTimeRaiseFeeList())) {
            poiShippingInfo.setTimeMarkupExecuteType(MarkupExecuteTypeEnum.ENABLE.getCode());

            List<TimeMarkupFactor> timeMarkupFactors = request.getTimeRaiseFeeList().stream()
                    .map(this::convertToTimeMarkupFactor)
                    .collect(Collectors.toList());

            poiShippingInfo.setTimeMarkupFactors(JSON.toJSONString(timeMarkupFactors));
        } else {
            poiShippingInfo.setTimeMarkupExecuteType(MarkupExecuteTypeEnum.DISABLE.getCode());
        }
    }

    /**
     * 转换时间加价因子
     */
    private TimeMarkupFactor convertToTimeMarkupFactor(TimeRaiseFee timeRaiseFee) {
        TimeMarkupFactor factor = new TimeMarkupFactor();
        String timeRange = timeRaiseFee.getStartTime() + "-" + timeRaiseFee.getEndTime();
        factor.setTimeRange(timeRange);
        factor.setMarkupNum(new BigDecimal(timeRaiseFee.getRaiseFee()).doubleValue());
        return factor;
    }
}
