package com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/8 19:56
 **/
@Data
public class OriginPoiShippingInfoDTO implements Serializable {
    private String app_shipping_code;

    private Long mt_shipping_id;

    private List<OriginCoordination> area;

    private Double min_price;

    private Double shipping_fee;

    private Integer type;

    private String shipping_period_name;

    private String time_range;

    private String app_poi_code;

    private String logistics_code;

    @Data
    public static class OriginCoordination implements Serializable{
        /**
         * 纬度*1000000
         */
        private Long x;

        /**
         * 经度*1000000
         */
        private Long y;
    }
}
