package com.sankuai.meituan.shangou.empower.ocms.channel.service.impl.mtbrand;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultCode;
import com.sankuai.meituan.shangou.empower.ocms.channel.common.ResultGenerator;
import com.sankuai.meituan.shangou.empower.ocms.channel.constant.ProjectConstant;
import com.sankuai.meituan.shangou.empower.ocms.channel.domain.ChannelStoreDO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ChannelResponseDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.OriginPoiShippingInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt.ShippingData;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.ChannelPoiShippingService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.CopChannelStoreService;
import com.sankuai.meituan.shangou.empower.ocms.channel.service.converter.MtConverterService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 新供给侧美团渠道门店配送服务
 * （一租户多品牌需求中，将新供给和歪马的服务代码拆分开来）
 *
 * <AUTHOR>
 */
@Service("mtBrandChannelPoiShippingService")
@Slf4j
public class MtBrandChannelPoiShippingServiceImpl implements ChannelPoiShippingService {

    @Resource
    private MtBrandChannelGateService mtBrandChannelGateService;

    @Resource
    private CopChannelStoreService copChannelStoreService;

    @Resource
    private MtConverterService mtConverterService;

    @Value("${mt.url.base}" + "${mt.url.shoplist}")
    private String shoplistUrl;

    @Value("${mt.url.base}" + "${mt.url.shippingDelete}")
    private String shippingDeleteUrl;

    @Value("${mt.url.base}" + "${mt.url.shippingSave}")
    private String shippingSaveUrl;

    @Value("${mt.url.base}" + "${mt.url.shippingList}")
    private String shippingListUrl;

    @Value("${mt.url.base}" + "${mt.url.corporateShippingList}")
    private String corporateShippingListUrl;

    @Value("${mt.url.base}" + "${mt.url.shippingBatchSave}")
    private String shippingBatchSaveUrl;

    @Value("${mt.url.base}" + "${mt.url.corporateShippingBatchSave}")
    private String corporateShippingBatchSaveUrl;

    @Override
    public ResultStatus updatePoiShipping(UpdatePoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getShopId()));
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtBrandChannelPoiShippingServiceImpl.updatePoiShipping  获取app_shipping_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO;
        Map<String, Object> getResult;
        //保存新配送范围
        bizParam = Maps.newHashMap();
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.MT_AREA, generateArea(request));
        bizParam.put(ProjectConstant.MT_MIN_PRICE, BigDecimal.valueOf(request.getMinOrderPrice()).scaleByPowerOfTen(-2).setScale(2, BigDecimal.ROUND_DOWN).toString());
        bizParam.put(ProjectConstant.MT_APP_SHIPPING_CODE, "1");
        bizParam.put(ProjectConstant.MT_TYPE, "1");
        getResult = mtBrandChannelGateService.sendPost(shippingSaveUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(getResult)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        channelResponseDTO = JSON.parseObject(JSON.toJSONString(getResult), ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }

        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    @Override
    public ResultStatus batchUpdatePoiShipping(BatchUpdatePoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getShopId()));
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtBrandChannelPoiShippingServiceImpl.updatePoiShipping  获取app_shipping_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.SHIPPING_DATA, JSON.toJSONString(ShippingData.convertFromPoiShippingInfo(request.getPoiShippingInfo())));

        Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(shippingBatchSaveUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap), ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());
    }

    @Override
    public ResultStatus resetPoiShipping(ResetPoiShippingRequest resetPoiShippingRequest) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus deletePoiShipping(DeletePoiShippingRequest deletePoiShippingRequest) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public QueryPoiShippingResponse queryPoiShipping(QueryPoiShippingRequest request) {
        //获取系统级参数
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());

        //获取应用级参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getPoiId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("ChannelPoiShippingService.queryPoiShipping获取app_poi_code失败");
            return new QueryPoiShippingResponse(ResultCode.FAIL.getCode(), "查询渠道门店配送范围时获取app_poi_code失败", null);
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);

        //调用开放平台
        log.info("query channel poi shipping list start, baseRequest:{}, bizParam:{}", baseRequest, bizParam);
        Map<String, Object> resultMap = mtBrandChannelGateService.sendGet(shippingListUrl, null, baseRequest, bizParam);
        log.info("query channel poi shipping list end, result:{}", resultMap);

        if (MapUtils.isEmpty(resultMap) || Objects.isNull(resultMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(resultMap.get(ProjectConstant.DATA))) {
            return new QueryPoiShippingResponse(ResultCode.FAIL.getCode(), "调用渠道查询配送范围失败", null);
        }

        JSONArray jsonArray = (JSONArray) resultMap.get(ProjectConstant.DATA);
        List<OriginPoiShippingInfoDTO> originPoiShippingInfoDTOS = jsonArray.toJavaList(OriginPoiShippingInfoDTO.class);

        // 组装数据
        return new QueryPoiShippingResponse(ResultCode.SUCCESS.getCode(), "", mtConverterService.poiShippingInfoMapping(originPoiShippingInfoDTOS));
    }

    @Override
    public QueryPoiShippingAreaResponse batchQueryPoiShippingAreaInfo(BatchQueryPoiShippingAreaRequest request) {
        return new QueryPoiShippingAreaResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public QueryPoiShippingAreaResponse queryPoiShippingAreaInfo(QueryPoiShippingRequest request) {
        return new QueryPoiShippingAreaResponse(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getCode(), ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg(), null);
    }

    @Override
    public ResultStatus updatePoiRegularPeriodShippingByShippingAreaId(UpdatePoiRegularPeriodShippingByShippingAreaIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus updatePoiSpecialPeriodShippingByShippingAreaId(UpdatePoiSpecialPeriodShippingByShippingAreaIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public ResultStatus deletePoiShippingByShippingAreaId(DeletePoiShippingByShippingIdRequest request) {
        return ResultGenerator.genFailResult(ResultCode.UNSUPPORTED_BUSINESS_TYPE.getMsg());
    }

    @Override
    public QueryPoiShippingResponse queryCorporatePoiShipping(QueryPoiShippingRequest request) {
        //获取系统级参数
        BaseRequest baseRequest = new BaseRequest().setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId());

        //获取应用级参数
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getPoiId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("ChannelPoiShippingService.queryPoiShipping获取app_poi_code失败");
            return new QueryPoiShippingResponse(ResultCode.FAIL.getCode(), "查询渠道门店配送范围时获取app_poi_code失败", null);
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);

        //调用开放平台
        log.info("query channel poi shipping list start, baseRequest:{}, bizParam:{}", baseRequest, bizParam);
        Map<String, Object> resultMap = mtBrandChannelGateService.sendGet(corporateShippingListUrl, null, baseRequest, bizParam);
        log.info("query channel poi shipping list end, result:{}", resultMap);

        if (MapUtils.isEmpty(resultMap) || Objects.isNull(resultMap.get(ProjectConstant.DATA)) || ProjectConstant.NG.equals(resultMap.get(ProjectConstant.DATA))) {
            return new QueryPoiShippingResponse(ResultCode.FAIL.getCode(), "调用渠道查询配送范围失败", null);
        }

        JSONArray jsonArray = (JSONArray) resultMap.get(ProjectConstant.DATA);
        List<OriginPoiShippingInfoDTO> originPoiShippingInfoDTOS = jsonArray.toJavaList(OriginPoiShippingInfoDTO.class);

        // 组装数据
        return new QueryPoiShippingResponse(ResultCode.SUCCESS.getCode(), "", mtConverterService.poiShippingInfoMapping(originPoiShippingInfoDTOS));
    }

    @Override
    public ResultStatus batchUpdateCorporatePoiShipping(BatchUpdatePoiShippingRequest request) {
        BaseRequest baseRequest = new BaseRequest()
                .setChannelId(request.getChannelId())
                .setTenantId(request.getTenantId())
                .setStoreIdList(Lists.newArrayList(request.getShopId()));
        Map<String, String> bizParam = Maps.newHashMap();
        String appPoiCode = getAppPoiCode(request.getTenantId(), request.getChannelId(), request.getShopId());
        if (StringUtils.isEmpty(appPoiCode)) {
            log.error("MtBrandChannelPoiShippingServiceImpl.updatePoiShipping  获取app_shipping_code失败");
            return ResultGenerator.genFailResult("美团渠道同步配送范围失败");
        }
        bizParam.put(ProjectConstant.APP_POI_CODE, appPoiCode);
        bizParam.put(ProjectConstant.SHIPPING_DATA, JSON.toJSONString(ShippingData.convertFromPoiShippingInfo(request.getPoiShippingInfo())));

        Map<String, Object> resultMap = mtBrandChannelGateService.sendPost(corporateShippingBatchSaveUrl, null, baseRequest, bizParam);
        if (MapUtils.isEmpty(resultMap)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围失败");
        }
        ChannelResponseDTO channelResponseDTO = JSON.parseObject(JSON.toJSONString(resultMap), ChannelResponseDTO.class);
        if (Objects.isNull(channelResponseDTO)) {
            return ResultGenerator.genFailResult("调用渠道保存配送范围返回数据解析失败");
        }
        if (channelResponseDTO.isSuccess()) {
            return ResultGenerator.genSuccessResult();
        }
        return ResultGenerator.genFailResult(channelResponseDTO.getErrorMsg());

    }

    private String generateArea(UpdatePoiShippingRequest request) {
        JSONArray jsonArray = new JSONArray();
        for (Coordinate coordinate : request.getCoordinates()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("x", BigDecimal.valueOf(coordinate.getLatitude()).scaleByPowerOfTen(6).intValue());
            jsonObject.put("y", BigDecimal.valueOf(coordinate.getLongitude()).scaleByPowerOfTen(6).intValue());
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJSONString();
    }

    private String getAppPoiCode(long tenantId, int channelId, long shopId) {
        Map<String, ChannelStoreDO> pois = copChannelStoreService.getChannelPoiCode(tenantId, channelId, Arrays.asList(shopId));
        if (!pois.containsKey("" + tenantId + "-" + channelId + "-" + shopId)) {
            return null;
        }
        return pois.get("" + tenantId + "-" + channelId + "-" + shopId).getChannelOnlinePoiCode();
    }
}

