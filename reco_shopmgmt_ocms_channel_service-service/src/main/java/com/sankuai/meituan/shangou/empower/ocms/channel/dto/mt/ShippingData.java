package com.sankuai.meituan.shangou.empower.ocms.channel.dto.mt;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/14
 * @email jianglilin02@meituan
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShippingData {

    private int app_shipping_code;

    //该配送区域的起送价，单位是元。创建时必传，更新时非必传
    private double min_price;

    private int type = 1;

    private String area;

    //<------以下为新增字段------>
    private Long mt_shipping_id;

    //该配送区域的配送费，单位是元。
    private Double shipping_fee;

    //按距离加价规则的执行类型：1-启用，2-不启用，3-保持原值 如果门店没有初始规则且不需要，则不传即可
    private Integer distance_markup_execute_type;
    //按距离加价， 1.上传多个规则时，需按从小到大顺序上传 2.最多支持设置5个加价规则 3.传启用则新上传的规则覆盖原有规则 4.传不启用则删除已有规则
    private String distance_markup_factors;

    //按重量加价规则的执行类型：1-启用，2-不启用，3-保持原值 如果门店没有初始规则且不需要，则不传即可
    private Integer weight_markup_execute_type;
    //按重量加价， 1.上传多个规则时，需按从小到大顺序上传 2.最多支持设置5个加价规则 3.传启用则新上传的规则覆盖原有规则 4.传不启用则删除已有规则
    private String weight_markup_factors;

    //按时段加价规则的执行类型：1-启用，2-不启用，3-保持原值 如果门店没有初始规则且不需要，则不传即可
    private Integer time_markup_execute_type;
    //按时段加价， 1.最多支持设置5个加价规则 2.传启用则新上传的规则覆盖原有规则 3.传不启用则删除已有规则
    private String time_markup_factors;

    //配送范围对应的配送方式，若操作的配送范围对应配送方式为企客远距离，则必传4015。若为企客xx达，则可不传。
    private String logistics_code;

    public static List<ShippingData> convertFromPoiShippingInfo(List<PoiShippingInfo> poiShippingInfoList) {
        if (CollectionUtils.isEmpty(poiShippingInfoList)) {
            return Lists.newArrayList();
        }

        return poiShippingInfoList
                .stream()
                .map(
                        poiShippingInfo -> {
                            ShippingData shippingData = new ShippingData();
                            shippingData.setApp_shipping_code();
                            shippingData.setMin_price();
                            shippingData.setType();
                            shippingData.setArea();
                            shippingData.setMt_shipping_id();
                            shippingData.setShipping_fee();
                            shippingData.setDistance_markup_execute_type();
                            shippingData.setDistance_markup_factors();
                            shippingData.setWeight_markup_execute_type();
                            shippingData.setWeight_markup_factors();
                            shippingData.setTime_markup_execute_type();
                            shippingData.setTime_markup_factors();
                            shippingData.setLogistics_code();

                            shippingData.setApp_shipping_code(poiShippingInfo.getAppShippingCode());
                            shippingData.setMin_price(BigDecimal.valueOf(poiShippingInfo.getMinPrice()).scaleByPowerOfTen(-2).setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
                            shippingData.setType(1);
	                        shippingData.setArea(
			                        JSON.toJSONString(poiShippingInfo
					                        .getCoordinateList()
					                        .stream()
					                        .map(
							                        coordinate -> {
								                        ShippingAreaInfo areaInfo = new ShippingAreaInfo();
								                        areaInfo.setX(BigDecimal.valueOf(coordinate.getLatitude()).scaleByPowerOfTen(6).intValue());
								                        areaInfo.setY(BigDecimal.valueOf(coordinate.getLongitude()).scaleByPowerOfTen(6).intValue());
								                        return areaInfo;
							                        }
					                        ).collect(Collectors.toList()))

	                        );

                            return shippingData;
                        }
                ).collect(Collectors.toList());

    }
}
